{"common": {"income": "Income", "expense": "Expense", "uncategorized": "Uncategorized", "today": "Today", "day": "day", "yesterday": "Yesterday", "month": "Month", "save": "Save", "cancel": "Cancel", "delete": "Delete", "ok": "OK", "error": "Error", "edit": "Edit", "add": "Add", "category": "Category", "amount": "Amount", "note": "Note", "date": "Date", "member": "Member", "refunded": "Refunded", "refund": "Refund", "partialRefund": "Partial Refund", "alreadyRefunded": "Already Refunded", "enterRefundAmount": "Enter refund amount", "pleaseEnterAmount": "Please enter an amount", "invalidRefundAmount": "Invalid refund amount. Maximum: {{max}}", "refundFailed": "Failed to process refund", "allMembers": "All Members", "monthlyBudget": "Monthly Budget", "transactionRecord": "Transactions", "all": "All", "noTransactions": "No transactions yet", "clickAddButtonToRecord": "Click the plus button in the bottom right corner to record", "monthlyIncome": "Monthly Income", "monthlyExpense": "Monthly Expense", "yearly": "Yearly", "custom": "Custom", "monthly": "Monthly", "tag": "Tag", "noData": "No data", "confirmDeleteMember": "Confirm Delete Member", "confirmDeleteTag": "Confirm Delete Tag", "confirmDeleteTransaction": "Confirm Delete Transaction", "confirmDeleteTransactionMessage": "Are you sure you want to delete this transaction? This action cannot be undone.", "weekdays": {"mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun"}, "months": {"1": "Jan", "2": "Feb", "3": "Mar", "4": "Apr", "5": "May", "6": "Jun", "7": "Jul", "8": "Aug", "9": "Sep", "10": "Oct", "11": "Nov", "12": "Dec"}, "close": "Close", "to": "to", "loading": "Loading", "loadMore": "Load more", "noMoreData": "No more data", "search": "Search note/category/member", "used": "Used", "remaining": "Remaining", "dailyAverage": "Daily Average", "daysLeft": " days left", "members": "Members", "budget": "Monthly Budget", "noBudget": "No Monthly Budget", "confirm": "Confirm", "thanks": "Thanks", "confirmDelete": "Confirm Delete", "deleteMemberFailed": "Delete member failed", "memberName": "Member name", "budgetAmount": "Monthly Budget Amount", "addMember": "Add member", "addMemberFailed": "Add member failed", "updateMemberFailed": "Update member failed", "defaultMemberCannotBeDeleted": "Default member cannot be deleted", "noMembers": "No members", "addMembersToRecord": "Add family members to record", "alert": "<PERSON><PERSON>", "pleaseInputTagName": "Please input tag name", "addTagFailed": "Add tag failed", "updateTagFailed": "Update tag failed", "deleteTagFailed": "Delete tag failed", "noTags": "No tags", "clickAddButtonToAddTag": "Click the plus button in the bottom right corner to add tag", "addNewTag": "Add New Tag", "tagName": "Tag name", "emailUnavailable": "Email function is not available", "noMember": "No Member", "none": "None", "updateBudgetFailed": "Update budget failed", "noBudgetPrompt": "Set up your budget in the profile page to track your spending\nor switch to a member with a set budget\n or to hide this module in the settings", "confirmDeleteTagPrompt": "Are you sure you want to delete this tag?", "excludeFromBudget": "Exclude from monthly budget"}, "home": {"title": "Home", "stats": "Stats", "profile": "Profile", "noTransactions": "No transactions yet"}, "add": {"title": "Create", "editTitle": "Edit", "addIncome": "Add Income", "addExpense": "Add Expense", "favorites": "Favorites", "new": "New", "addToFavorites": "Add to Favorites", "noFavorites": "No favorites yet", "clickAddButtonToCreate": "Click the add to fav button in the create tab to create your first favorite", "saveFailed": "Save failed", "oneClickImport": "One-Click Import", "importRecommendedCategories": "Import Recommended Categories", "selectCategoriesToImport": "Select categories to import:", "importSelected": "Import Selected", "categoriesImported": "Categories imported successfully", "importFailed": "Failed to import categories", "shoppingPlatform": "Shopping Platform", "selectShoppingPlatform": "Select Shopping Platform (Optional)", "validation": {"amountRequired": "Please enter a valid amount greater than 0", "categoryRequired": "Please select a category"}, "recommendedCategories": {"expense": {"transportation": "Transportation", "dining": "Dining", "travel": "Travel", "shopping": "Shopping", "entertainment": "Entertainment", "medical": "Medical", "education": "Education", "housing": "Housing", "communication": "Communication", "clothing": "Clothing", "beauty": "Beauty", "sports": "Sports", "pets": "Pets", "gifts": "Gifts", "insurance": "Insurance"}, "income": {"salary": "Salary", "bonus": "Bonus", "investment": "Investment", "partTime": "Part-time", "giftMoney": "Gift Money", "refund": "Refund", "dividend": "Dividend", "rent": "Rent", "interest": "Interest", "other": "Other"}}}, "categories": {"title": "Categories", "addCategory": "Add Category", "categoryName": "Category name", "selectIcon": "Select Icon", "noCategories": "No categories yet", "income": "Income Categories", "expense": "Expense Categories", "categoryNameRequired": "Please input category name", "addCategoryFailed": "Add category failed", "updateCategoryFailed": "Update category failed", "deleteCategoryFailed": "Delete category failed", "pinToTopFailed": "<PERSON>n to top failed", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this category? Once deleted, it cannot be recovered.", "delete": "Delete", "cancel": "Cancel", "editCategory": "Edit Category", "customEmoji": "Custom emoji", "customEmojiPlaceholder": "Enter any emoji...", "confirm": "Confirm"}, "profile": {"profile": "Profile", "memberBudget": "Member Management And Monthly Budget", "exportExcel": "Export Excel", "manageCategories": "Manage Categories", "manageTags": "Manage Tags", "settings": "Settings", "budget": {"title": "Budget Settings", "monthly": "Monthly Budget", "daily": "Daily Budget"}, "premium": {"title": "Premium Feature", "description": "Unlock tag management and more premium features", "feature1": "Unlimited tags for better expense tracking", "feature2": "Manage Prepaid card", "feature3": "Manage shopping platforms", "feature4": "Planned purchases list", "purchase": "Upgrade to Premium", "badge": "Premium", "success": "Purchase Successful", "enjoyFeatures": "Thank you for your purchase! You can now enjoy all premium features.", "failed": "Purchase Failed", "tryAgain": "Please try again later.", "notAvailable": "Product Not Available", "error": "Error", "requiredTitle": "Premium Feature", "requiredDesc": "Tags feature requires premium upgrade. Would you like to upgrade now?", "upgrade": "Upgrade", "alreadyPremium": "Already Premium", "alreadyPremiumDesc": "You already have premium access to all features.", "restore": "Restore Purchase", "redeemCode": "Use Redeem Code", "redeemTitle": "Redeem Code", "redeemDescription": "Enter a redeem code to get premium features for free", "redeemDescriptionNew": "Tap the button below to open Apple's redemption page where you can enter your promotional code", "openRedemption": "Open Redemption Page", "redeemPlaceholder": "Enter redeem code", "redeemSuccess": "Redeem Successful", "redeemSuccessMessage": "Congratulations! Your redeem code has been successfully redeemed. You can now enjoy all premium features.", "redeemFailed": "Redeem Failed", "redeemFailedMessage": "The redeem code is invalid or expired. Please check and try again.", "redeemError": "An error occurred during redemption. Please try again later.", "enterRedeemCode": "Please enter a redeem code"}, "export": {"title": "Export Transactions", "emailPlaceholder": "Enter your email address", "invalidEmail": "Please enter a valid email address", "success": "Export Successful", "sent": "The export file has been sent to your email", "failed": "Export failed, please try again later", "emailUnavailable": "Email function is not available", "exportToEmail": "Export to Email", "exportToLocal": "Save to Device", "saveFile": "Save File", "sharingUnavailable": "Sharing is not available on this device", "localExportSuccess": "File saved successfully"}, "rateApp": "Rate App", "rateAppMessage": "If you like this app, please rate us in the app store!", "rateAppThanks": "Thank you for your support!", "importExcel": "Import Excel", "import": {"title": "Import Transactions", "selectFile": "Select Excel File", "processing": "Processing...", "success": "Import Successful", "successMessage": "Successfully imported {count} transactions", "failed": "Import Failed", "invalidFormat": "Invalid file format. Please use Excel (.xlsx) file", "fileNotFound": "File not found", "instructions": "If the file is exported by this app, it can be imported directly. If the file is exported by other apps, it should contain the following headers: date, type (income/expense), category, amount, note.", "instructionsPremium": "Tags feature is only available for premium users. Non-premium users will have tags column ignored during import."}, "appName": "NineCents", "dataWarning": {"title": "Data Safety Reminder", "message": "Deleting the app will cause all data to be lost! It is recommended to regularly export Excel files to backup data.", "exportReminder": "💡Tip: Click 'Export Excel' below to backup your data", "shortText": "Data Safety: All app data are stored locally"}}, "settings": {"title": "Settings", "general": "General Settings", "display": "Display Settings", "features": "Feature Management", "currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "languageOptions": {"zh": "中文", "en": "English"}, "hideBudgetModule": "Hide Home Budget Module", "hideMemberSection": "Hide Member <PERSON><PERSON>", "hideExcludeFromBudget": "Hide Exclude from Budget Option", "hideShoppingPlatformSection": "Hide Shopping Platform Selection", "personalNote": "Personal Note", "personalNoteDescription": "Add a personal reminder (max 100 characters)", "personalNotePlaceholder": "e.g., Don't buy LEGO this month!", "personalNoteDefault": "Data is stored locally. Remember to export before deleting the app. You can change this message in settings or hide it.", "prepaidCards": "Manage Prepaid Card", "shoppingPlatforms": "Manage Shopping Platform", "theme": "Theme", "lightMode": "Light Mode", "darkMode": "Dark Mode (Same as Light)", "appearance": "Appearance", "success": "Success", "error": "Error", "currencyUpdated": "Currency has been updated", "updateFailed": "Update failed, please try again", "premiumRequired": "Premium Feature", "premiumRequiredMessage": "This feature requires premium membership. Would you like to upgrade?", "upgrade": "Upgrade"}, "budget": {"title": "Budget", "monthly": "Monthly Budget", "daily": "Daily Budget", "totalBudget": "Total Monthly Budget"}, "stats": {"detail": "StatsDetail", "detailAnalysis": "Detail Analysis", "yearlyStatistics": "Yearly Statistics", "selectYear": "Select Year", "balance": "Balance", "month": "Month", "yearlyTotals": "Totals"}, "onboarding": {"welcome": "Welcome to NineCents", "welcomeMessage": "Let's quickly set up and start your accounting journey!", "step1Title": "Tap here to start recording", "step1Description": "Tap the plus button in the bottom right corner to add your first transaction", "step2Title": "Select a category", "step2Description": "Tap here to expand category selection and choose an appropriate category", "addCategory": "Add Category", "addTransaction": "Add Transaction", "skip": "Skip Guide", "next": "Next", "finish": "Finish Guide", "getStarted": "Get Started", "skipConfirm": "Are you sure you want to skip the onboarding guide?", "skipConfirmMessage": "You can manage categories and record transactions later in the profile page.", "gotIt": "Got it"}, "prepaidCards": {"title": "Manage Prepaid Card", "addCard": "Add Prepaid Card", "cardName": "Card Name", "cardType": "Card Type", "amountCard": "Amount Card", "countCard": "Count Card", "cardBalance": "Card Balance", "cardCount": "Card Count", "enterCardCount": "Enter card count", "rechargeDate": "Recharge Date", "expiryDate": "Expiry Date", "optional": "(Optional)", "selectExpiryDate": "Select expiry date", "clearExpiryDate": "Clear expiry date", "noCards": "No prepaid cards yet", "addFirstCard": "Click the button above to add your first prepaid card", "editCard": "Edit Card", "deleteCard": "Delete Card", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this prepaid card?", "cardNameRequired": "Please enter card name", "invalidBalance": "Please enter a valid balance", "invalidCountBalance": "Please enter a valid count", "addCardFailed": "Failed to add card", "updateCardFailed": "Failed to update card", "deleteCardFailed": "Failed to delete card", "premiumFeature": "Premium Feature", "premiumRequired": "Prepaid card management requires premium membership", "upgradePrompt": "Would you like to upgrade to premium?", "useCard": "Use Card", "usageAmount": "Usage Amount", "usageNote": "Usage Note", "usageDate": "Usage Date", "currentBalance": "Current Balance", "enterUsageAmount": "Enter usage amount", "enterUsageNote": "Enter usage note (optional)", "invalidUsageAmount": "Please enter a valid usage amount", "insufficientBalance": "Insufficient balance", "insufficientCount": "Insufficient count", "useCardFailed": "Failed to use card", "usageHistory": "Usage History", "noUsageRecords": "No usage records yet", "loadRecordsFailed": "Failed to load usage records", "editUsage": "Edit Usage Record", "confirmDeleteUsage": "Are you sure you want to delete this usage record?", "deleteUsageFailed": "Failed to delete usage record"}, "shoppingPlatforms": {"title": "Shopping Platform Management", "addPlatform": "Add Platform", "platformName": "Platform Name", "platformIcon": "Platform Icon", "noPlatforms": "No shopping platforms yet", "addFirstPlatform": "Click the button above to add your first shopping platform", "editPlatform": "Edit Platform", "deletePlatform": "Delete Platform", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this shopping platform?", "platformNameRequired": "Please enter platform name", "addPlatformFailed": "Failed to add platform", "updatePlatformFailed": "Failed to update platform", "deletePlatformFailed": "Failed to delete platform", "defaultPlatforms": "Default Platforms", "customPlatforms": "Custom Platforms", "oneClickImport": "One-Click Import", "importRecommendedPlatforms": "Import Recommended Shopping Platforms", "selectPlatformsToImport": "Select platforms to import:", "importSelected": "Import Selected", "platformsImported": "Platforms imported successfully", "importFailed": "Failed to import platforms", "recommendedPlatforms": {"amazon": "Amazon", "taobao": "Taobao", "tmall": "Tmall", "jd": "JD.com", "pinduoduo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "douyin": "<PERSON><PERSON><PERSON>", "xianyu": "<PERSON><PERSON><PERSON>", "kuaituantuan": "Kuaituantuan", "ebay": "eBay", "aliexpress": "AliExpress", "walmart": "Walmart", "target": "Target", "bestbuy": "Best Buy", "costco": "Costco", "shopee": "<PERSON>ee", "lazada": "<PERSON><PERSON><PERSON>", "rakuten": "Ra<PERSON><PERSON>", "mercadolibre": "MercadoLibre", "flipkart": "<PERSON><PERSON><PERSON><PERSON>", "zalando": "<PERSON><PERSON><PERSON>"}}, "plannedPurchases": {"title": "Planned Purchases List", "addItem": "Add Item", "itemName": "Item Name", "targetPrice": "Target Price", "currentPrice": "Current Price", "note": "Note", "priority": "Priority", "priorityLow": "Low", "priorityMedium": "Medium", "priorityHigh": "High", "completed": "Completed", "pending": "Pending", "noItems": "No planned purchases yet", "addFirstItem": "Tap the button above to add your first planned purchase item", "editItem": "<PERSON>em", "deleteItem": "Delete Item", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this planned purchase item?", "itemNameRequired": "Please enter item name", "targetPriceRequired": "Please enter target price", "invalidTargetPrice": "Please enter a valid target price", "notSet": "Not set", "addItemFailed": "Failed to add item", "updateItemFailed": "Failed to update item", "deleteItemFailed": "Failed to delete item", "markCompleted": "<PERSON> as Completed", "markPending": "<PERSON> as Pending", "optional": "(Optional)", "completeItem": "Complete Item", "finalPrice": "Final Purchase Price", "completionNote": "Completion Note", "completionNotePlaceholder": "Add notes about your purchase...", "completeItemFailed": "Failed to complete item"}, "lending": {"title": "Personal Lending", "lend": "Lend", "borrow": "Borrow", "addRecord": "Add Record", "editRecord": "Edit Record", "recordType": "Record Type", "personName": "Person Name", "amount": "Amount", "interestRate": "Interest Rate", "lendingDate": "Lending Date", "dueDate": "Due Date", "note": "Note", "optional": "(Optional)", "enterInterestRate": "Enter interest rate", "selectDueDate": "Select due date", "clearDueDate": "Clear due date", "enterNote": "Enter note", "noRecords": "No lending records yet", "addFirstRecord": "Click the button above to add your first lending record", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this lending record?", "personNameRequired": "Please enter person name", "invalidAmount": "Please enter a valid amount", "invalidInterestRate": "Please enter a valid interest rate", "addRecordFailed": "Failed to add record", "updateRecordFailed": "Failed to update record", "deleteRecordFailed": "Failed to delete record", "recordDetail": "Record Detail", "recordNotFound": "Record not found", "originalAmount": "Original Amount", "totalRepaid": "Total Repaid", "remainingAmount": "Remaining Amount", "repaymentHistory": "Repayment History", "noRepayments": "No repayments yet", "addRepayment": "Add Repayment", "repaymentAmount": "Repayment Amount", "repaymentDate": "Repayment Date", "enterRepaymentAmount": "Enter repayment amount", "invalidRepaymentAmount": "Please enter a valid repayment amount", "addRepaymentFailed": "Failed to add repayment"}, "tags": {"title": "Tag Management", "tags": "Tags", "selectedTags": "Selected %{count} tags"}}